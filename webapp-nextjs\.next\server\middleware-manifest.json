{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "d18a49f04bb95c1f3c14aeedd3c00477", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a5edccc76c8cf996bb7f1946ab23517f5f2896e6ecedd3defe95d61b5bd6fd67", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cb00912ce9dd821aaccc68131296d7bf9ccd78408b61918336dbdec7a5fdd594"}}}, "sortedMiddleware": ["/"], "functions": {}}